# Apple Sign-In Quick Start Guide

Get Apple Sign-In working in 5 minutes with this quick start guide.

## Prerequisites Checklist

- [ ] Apple Developer Account with Ad<PERSON> role
- [ ] Team ID: `598AQBZ36R`
- [ ] App Store Connect API key generated
- [ ] Ruby and Bundler installed
- [ ] Flutter SDK installed

## Step 1: Environment Setup (2 minutes)

### 1.1 Create Environment File

```bash
# Create .env file in project root
cat > .env << EOF
APPLE_ID=<EMAIL>
APPLE_TEAM_ID=598AQBZ36R
APP_STORE_CONNECT_API_KEY_ID=your-key-id
APP_STORE_CONNECT_API_ISSUER_ID=your-issuer-id
APP_STORE_CONNECT_API_KEY_CONTENT=your-base64-encoded-key
EOF

# Add to .gitignore
echo ".env" >> .gitignore
```

### 1.2 Load Environment

```bash
source .env
```

## Step 2: Install Dependencies (1 minute)

```bash
# Install Fastlane dependencies
cd fastlane && bundle install && cd ..

# Install test dependencies
npm install
```

## Step 3: Setup Apple Sign-In (1 minute)

### For Development Environment

```bash
bundle exec fastlane ios setup_apple_signin environment:development
```

### For All Environments

```bash
bundle exec fastlane setup_apple_signin_complete
```

## Step 4: Validate Setup (1 minute)

```bash
# Check configuration status
bundle exec fastlane ios show_apple_signin_status

# Run validation
bundle exec fastlane validate_complete_apple_signin_setup
```

## Step 5: Test Everything (Optional)

```bash
# Run comprehensive tests
./scripts/run_apple_signin_tests.sh
```

## Quick Commands Reference

```bash
# Setup development environment
bundle exec fastlane ios setup_apple_signin environment:development

# Setup production environment  
bundle exec fastlane ios setup_apple_signin environment:production

# Setup all platforms
bundle exec fastlane setup_apple_signin_complete

# Check status
bundle exec fastlane ios show_apple_signin_status

# Clean and reset
bundle exec fastlane ios clean_apple_signin_config

# Run tests
./scripts/run_apple_signin_tests.sh
```

## Troubleshooting Quick Fixes

### Issue: Authentication Failed
```bash
# Verify credentials
echo $APPLE_ID
echo $APPLE_TEAM_ID

# Re-export if needed
export APPLE_ID="<EMAIL>"
```

### Issue: Bundle ID Not Found
```bash
# Register bundle ID
bundle exec fastlane ios register_bundle_id_if_needed \
  bundle_id:com.algomash.bloomg.bloomg-flutter.dev \
  name:"Bloomg Flutter Development"
```

### Issue: Provisioning Profile Error
```bash
# Clean and regenerate
bundle exec fastlane ios clean_apple_signin_config
bundle exec fastlane ios setup_certificates_and_profiles environment:development
```

## Success Indicators

✅ **Setup Complete** when you see:
- "Apple Sign-In setup completed for [environment]"
- "Complete Apple Sign-In setup finished!"
- All validation checks pass

✅ **Ready for Testing** when:
- Flutter analyze shows 0 issues
- Apple Sign-In buttons appear in login/signup pages
- Web tests pass with Playwright

✅ **Ready for Deployment** when:
- All environments configured
- CI/CD pipeline runs successfully
- Post-deployment validation passes

## Next Steps

1. **Test on Device**: Run the app on iOS device/simulator
2. **Test Web Flow**: Test Apple Sign-In in web browser
3. **Deploy to Staging**: Use staging environment for testing
4. **Monitor Logs**: Check authentication logs in Firebase Console

## Getting Help

- **Full Documentation**: `docs/fastlane_apple_signin_setup.md`
- **API Configuration**: `fastlane/AppStoreConnectAPI.md`
- **Test Logs**: Check `~/.fastlane/logs/fastlane.log`
- **Status Check**: `bundle exec fastlane ios show_apple_signin_status`

## Emergency Reset

If something goes wrong, reset everything:

```bash
# Clean all configuration
bundle exec fastlane ios clean_apple_signin_config

# Remove certificates and profiles
rm -rf fastlane/certificates fastlane/profiles

# Start over
bundle exec fastlane ios setup_apple_signin environment:development
```

---

**🎉 You're all set!** Apple Sign-In should now be working across all platforms.
