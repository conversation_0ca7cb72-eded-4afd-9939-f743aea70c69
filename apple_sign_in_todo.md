# Apple Sign-In Integration Todo

## Overview
This document tracks the implementation of Apple Sign-In authentication for iOS, Android, and Web platforms in the Bloomg Flutter app, following Firebase Authentication patterns and maintaining consistency with existing Google Sign-In implementation.

## Current State Analysis
- ✅ Apple Sign-In package already added: `sign_in_with_apple: ^7.0.1`
- ✅ Firebase Console configured with Apple provider (Team ID: 598AQBZ36R, Key ID: 99PLQH6WMP)
- ✅ UI components exist: `AppleSignInButton` widget created
- ✅ Plugin registration confirmed in iOS/macOS GeneratedPluginRegistrant
- ⚠️ Authentication logic not implemented (placeholder TODOs in login/signup pages)
- ⚠️ Repository methods not implemented for Apple Sign-In

## Platform-Specific Requirements

### Firebase Console (Already Configured ✅)
- Team ID: 598AQBZ36R
- Key ID: 99PLQH6WMP  
- Service ID: com.algomash.bloomg.signin
- Private key configured
- Callback URL: https://bloomg-flutter.firebaseapp.com/__/auth/handler

### iOS Configuration
- [ ] **Update iOS Bundle ID registration** in Firebase Console if needed
- [ ] **Add Sign In with Apple capability** in Xcode project
  - Open `ios/Runner.xcworkspace` 
  - Select Runner target → Signing & Capabilities
  - Add "Sign In with Apple" capability
- [ ] **Verify Info.plist** contains proper URL schemes (should be auto-configured)

### Android Configuration  
- [ ] **Register SHA-1 signature** in Firebase Console (if not already done)
- [ ] **No additional Android-specific config needed** (handled by Firebase SDK)

### Web Configuration
- [ ] **Domain verification** with Apple (if using custom domain)
- [ ] **Test web callback URL** functionality

## Code Changes Required

### 1. Repository Layer Updates

#### File: `lib/auth/repository/auth_repository.dart`
- [ ] **Add Apple Sign-In method signature**
```dart
/// Signs in with Apple.
/// 
/// Throws a [LogInWithAppleFailure] if an exception occurs.
Future<void> logInWithApple();
```

- [ ] **Add Apple Sign-In exception class**
```dart
/// {@template log_in_with_apple_failure}
/// Thrown during the Apple sign in process if a failure occurs.
/// {@endtemplate}
class LogInWithAppleFailure implements Exception {
  const LogInWithAppleFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory LogInWithAppleFailure.fromCode(String code) {
    switch (code) {
      case 'invalid-credential':
        return const LogInWithAppleFailure(
          'The credential received is malformed or has expired.',
        );
      case 'operation-not-allowed':
        return const LogInWithAppleFailure(
          'Apple sign-in is not enabled.',
        );
      case 'user-disabled':
        return const LogInWithAppleFailure(
          'This user has been disabled.',
        );
      default:
        return const LogInWithAppleFailure();
    }
  }

  final String message;
}
```

#### File: `lib/auth/repository/firebase_auth_repository.dart`
- [ ] **Add Apple Sign-In import**
```dart
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
```

- [ ] **Implement logInWithApple method**
```dart
@override
Future<void> logInWithApple() async {
  final startTime = DateTime.now();

  _logger.logAuth(
    LoggingConstants.loginAttempt,
    'started',
    'apple_sign_in',
    'Apple Sign-In initiated',
  );

  try {
    // Check availability
    if (!await SignInWithApple.isAvailable()) {
      throw const LogInWithAppleFailure(
        'Apple Sign-In is not available on this device.',
      );
    }

    // Get Apple credentials
    final appleCredential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    // Create OAuth credential for Firebase
    final oauthCredential = OAuthProvider("apple.com").credential(
      idToken: appleCredential.identityToken,
      accessToken: appleCredential.authorizationCode,
    );

    // Sign in with Firebase
    final credential = await _firebaseAuth.signInWithCredential(oauthCredential);

    final duration = DateTime.now().difference(startTime);
    final user = _mapFirebaseUserToUserModel(credential.user!);

    // Save to local storage
    await _hiveService.saveUser(user);

    _logger
      ..logPerformance(
        'Apple Sign-In authentication',
        duration,
        'Apple authentication',
      )
      ..logAuth(
        LoggingConstants.loginSuccess,
        'completed',
        'apple_sign_in',
        'Apple authentication successful',
      );
  } on SignInWithAppleAuthorizationException catch (e) {
    _logger.logAuth(
      LoggingConstants.loginFailure,
      'failed',
      'apple_sign_in',
      'Apple authorization error: ${e.code} - ${e.message}',
    );

    // Handle specific Apple errors
    switch (e.code) {
      case AuthorizationErrorCode.canceled:
        throw const LogInWithAppleFailure('Apple Sign-In was cancelled.');
      case AuthorizationErrorCode.failed:
        throw const LogInWithAppleFailure('Apple Sign-In failed.');
      case AuthorizationErrorCode.invalidResponse:
        throw const LogInWithAppleFailure('Invalid Apple Sign-In response.');
      case AuthorizationErrorCode.notHandled:
        throw const LogInWithAppleFailure('Apple Sign-In not handled.');
      case AuthorizationErrorCode.unknown:
        throw const LogInWithAppleFailure('Unknown Apple Sign-In error.');
    }
  } on FirebaseAuthException catch (e) {
    _logger.logAuth(
      LoggingConstants.loginFailure,
      'failed',
      'apple_sign_in',
      'Firebase authentication error: ${e.code} - ${e.message}',
    );

    throw LogInWithAppleFailure.fromCode(e.code);
  } catch (e) {
    _logger.logAuth(
      LoggingConstants.loginFailure,
      'failed',
      'apple_sign_in',
      'Unexpected error during Apple sign-in: $e',
    );

    throw const LogInWithAppleFailure();
  }
}
```

#### File: `lib/auth/repository/auth_repository_impl.dart` (Mock Implementation)
- [ ] **Add mock Apple Sign-In method**
```dart
@override
Future<void> logInWithApple() async {
  _logger.logFirebase(
    'Apple Sign-In request',
    'started',
    'Mock Apple Sign-In attempt',
  );

  // Simulate network delay
  await Future<void>.delayed(const Duration(seconds: 2));

  _logger.logFirebase(
    'Apple Sign-In request',
    'success',
    'Mock Apple Sign-In successful',
  );

  // Emit authenticated user with Apple-like data
  _userController.add(
    _mockUser.copyWith(
      email: '<EMAIL>',
      displayName: 'Apple User',
    ),
  );
}
```

### 2. Cubit Layer Updates

#### File: `lib/auth/cubit/login_cubit.dart`
- [ ] **Add Apple Sign-In method**
```dart
/// Submits Apple Sign-In authentication.
Future<void> logInWithApple() async {
  if (isClosed) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'LoginCubit logInWithApple called after close',
      ),
    );
    return;
  }

  final startTime = DateTime.now();

  _logger.logAuth(
    LoggingConstants.loginAttempt,
    'started',
    'apple_sign_in',
    'Apple Sign-In initiated',
  );

  if (isClosed) return;
  emit(state.copyWith(status: FormStatus.submissionInProgress));

  try {
    await _authRepository.logInWithApple();

    // Check if cubit is still active before proceeding
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit closed during Apple authentication',
          'Skipping success state emission',
        ),
      );
      return;
    }

    final duration = DateTime.now().difference(startTime);
    _logger
      ..logPerformance(
        'Apple Sign-In authentication',
        duration,
        'Apple authentication',
      )
      ..logAuth(
        LoggingConstants.loginSuccess,
        'completed',
        'apple_sign_in',
        'Apple authentication successful',
      );

    if (!isClosed) {
      emit(state.copyWith(status: FormStatus.submissionSuccess));
    }
  } on LogInWithAppleFailure catch (e) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit closed during Apple authentication error',
          'Skipping error state emission',
        ),
      );
      return;
    }

    _logger.logAuth(
      LoggingConstants.loginFailure,
      'failed',
      'apple_sign_in',
      'Apple authentication error: ${e.message}',
    );

    if (!isClosed) {
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    }
  } catch (error, stackTrace) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit closed during Apple authentication error',
          'Skipping error state emission',
        ),
      );
      return;
    }

    _logger
      ..error(error, stackTrace)
      ..logAuth(
        LoggingConstants.loginFailure,
        'error',
        'apple_sign_in',
        'Unexpected error during Apple authentication',
      );

    if (!isClosed) {
      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
```

#### File: `lib/auth/cubit/signup_cubit.dart`
- [ ] **Add Apple Sign-In method**
```dart
/// Submits Apple Sign-In authentication.
Future<void> signUpWithApple() async {
  if (isClosed) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'SignupCubit signUpWithApple called after close',
      ),
    );
    return;
  }

  final startTime = DateTime.now();

  _logger.logAuth(
    LoggingConstants.signupAttempt,
    'started',
    'apple_sign_in',
    'Apple Sign-In registration initiated',
  );

  if (isClosed) return;
  emit(state.copyWith(status: FormStatus.submissionInProgress));

  try {
    await _authRepository.logInWithApple();

    // Check if cubit is still active before proceeding
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit closed during Apple authentication',
          'Skipping success state emission',
        ),
      );
      return;
    }

    final duration = DateTime.now().difference(startTime);
    _logger
      ..logPerformance(
        'Apple Sign-In registration',
        duration,
        'Apple authentication',
      )
      ..logAuth(
        LoggingConstants.signupSuccess,
        'completed',
        'apple_sign_in',
        'Apple registration successful',
      );

    if (!isClosed) {
      emit(state.copyWith(status: FormStatus.submissionSuccess));
    }
  } on LogInWithAppleFailure catch (e) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit closed during Apple authentication error',
          'Skipping error state emission',
        ),
      );
      return;
    }

    _logger.logAuth(
      LoggingConstants.signupFailure,
      'failed',
      'apple_sign_in',
      'Apple registration error: ${e.message}',
    );

    if (!isClosed) {
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    }
  } catch (error, stackTrace) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit closed during Apple authentication error',
          'Skipping error state emission',
        ),
      );
      return;
    }

    _logger
      ..error(error, stackTrace)
      ..logAuth(
        LoggingConstants.signupFailure,
        'error',
        'apple_sign_in',
        'Unexpected error during Apple registration',
      );

    if (!isClosed) {
      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
```

### 3. UI Layer Updates

#### File: `lib/auth/view/login_page.dart`
- [ ] **Update Apple Sign-In button onPressed callback**
```dart
// Replace the existing TODO with:
onPressed: () => context
    .read<LoginCubit>()
    .logInWithApple(),
```

#### File: `lib/auth/view/create_account_page.dart` 
- [ ] **Update Apple Sign-In button onPressed callback**
```dart
// Replace the existing TODO with:
onPressed: () => context
    .read<SignupCubit>()
    .signUpWithApple(),
```

## Platform Availability Considerations

### Apple Sign-In Button Visibility
- [ ] **Update button visibility logic** to check platform availability
- [ ] **Add availability check** in button widgets

```dart
// In AppleSignInButton widget or parent:
FutureBuilder<bool>(
  future: SignInWithApple.isAvailable(),
  builder: (context, snapshot) {
    if (snapshot.data == true) {
      return AppleSignInButton(...);
    }
    return const SizedBox.shrink(); // Hide if not available
  },
)
```

## Testing Requirements

### Manual Testing Checklist
- [ ] **iOS Device Testing**
  - [ ] Test sign-in flow on iOS device with Apple ID
  - [ ] Test with 2FA-enabled Apple ID
  - [ ] Test email privacy options (share/hide email)
  - [ ] Test sign-out and re-sign-in

- [ ] **Android Device Testing** 
  - [ ] Test sign-in flow on Android device
  - [ ] Test web-based OAuth flow
  - [ ] Test error handling for unavailable service

- [ ] **Web Testing**
  - [ ] Test sign-in flow in web browser
  - [ ] Test popup vs redirect flows
  - [ ] Test callback URL handling

### Unit Testing
- [ ] **Add unit tests** for Apple Sign-In repository methods
- [ ] **Add unit tests** for Apple Sign-In cubit methods
- [ ] **Update existing authentication tests** to include Apple Sign-In scenarios

### Integration Testing
- [ ] **Add Apple Sign-In** to authentication flow tests
- [ ] **Test error scenarios** (cancelled, failed, network issues)

## Error Handling & Edge Cases

### Error Scenarios to Handle
- [ ] **Apple Sign-In not available** on device/platform
- [ ] **User cancels** Apple Sign-In flow
- [ ] **Network connectivity** issues during sign-in
- [ ] **Firebase authentication** failures
- [ ] **Token validation** errors
- [ ] **Account linking** conflicts

### User Experience Considerations  
- [ ] **Loading states** during Apple Sign-In process
- [ ] **Error messages** for different failure scenarios
- [ ] **Fallback options** when Apple Sign-In unavailable
- [ ] **Privacy messaging** about Apple's email relay feature

## Documentation Updates

- [ ] **Update firebase_documentation.md** with Apple Sign-In examples
- [ ] **Update timeline.md** with Apple Sign-In implementation details
- [ ] **Create Apple Sign-In troubleshooting guide** if needed

## Pre-Deployment Checklist

- [ ] **Run flutter analyze** - must show 0 issues
- [ ] **Test on all target platforms** (iOS, Android, Web)
- [ ] **Verify Firebase Console** configuration matches code
- [ ] **Test with real Apple ID accounts** (not just development)
- [ ] **Verify proper error handling** for all scenarios
- [ ] **Check Apple Developer Console** configuration
- [ ] **Test email privacy features** work correctly

## Post-Implementation Notes

### Known Limitations
- Apple Sign-In requires iOS 13+ for native implementation
- Android uses web-based OAuth flow through Firebase
- Email privacy features may affect user identification logic
- First-time sign-in provides full name, subsequent sign-ins may not

### Future Enhancements
- Consider implementing Apple Sign-In specific user profile handling
- Add support for Sign In with Apple JS SDK for web optimization
- Implement token revocation for account deletion compliance
- Add Apple-specific analytics tracking

---

## Implementation Priority
1. **High**: Repository and Cubit layer implementation
2. **High**: UI button callback updates  
3. **Medium**: iOS Xcode capability configuration
4. **Medium**: Comprehensive testing
5. **Low**: Platform availability optimizations
6. **Low**: Documentation updates

**Estimated Implementation Time**: 4-6 hours
**Testing Time**: 2-3 hours  
**Total**: 6-9 hours
