import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:flutter/foundation.dart';

/// {@template camera_utils}
/// Utility functions for camera operations and configuration.
/// {@endtemplate}
class CameraUtils {
  /// Private constructor to prevent instantiation
  const CameraUtils._();

  /// Default video recording configurations for different quality levels
  static const Map<VideoQuality, Map<String, dynamic>> videoConfigurations = {
    VideoQuality.low: {
      'width': 640,
      'height': 480,
      'bitrate': 1000000, // 1 Mbps
      'frameRate': 24,
    },
    VideoQuality.medium: {
      'width': 1280,
      'height': 720,
      'bitrate': 3000000, // 3 Mbps
      'frameRate': 30,
    },
    VideoQuality.high: {
      'width': 1920,
      'height': 1080,
      'bitrate': 8000000, // 8 Mbps
      'frameRate': 30,
    },
    VideoQuality.ultraHigh: {
      'width': 3840,
      'height': 2160,
      'bitrate': 20000000, // 20 Mbps
      'frameRate': 30,
    },
  };

  /// Gets video configuration for the specified quality
  static Map<String, dynamic> getVideoConfiguration(VideoQuality quality) {
    return Map<String, dynamic>.from(
      videoConfigurations[quality] ?? videoConfigurations[VideoQuality.high]!,
    );
  }

  /// Determines optimal video quality based on device capabilities
  static VideoQuality getOptimalVideoQuality() {
    // In a real implementation, this would check device capabilities
    // For now, return high quality as default
    return VideoQuality.high;
  }

  /// Checks if the device supports the specified video quality
  static bool supportsVideoQuality(VideoQuality quality) {
    // In a real implementation, this would check device capabilities
    // For now, assume all qualities are supported
    return true;
  }

  /// Gets the recommended frame rate for face detection
  static int getRecommendedDetectionFrameRate() {
    // 10 FPS is sufficient for face detection while maintaining performance
    return 10;
  }

  /// Calculates the detection interval based on frame rate
  static Duration getDetectionInterval() {
    final frameRate = getRecommendedDetectionFrameRate();
    return Duration(milliseconds: (1000 / frameRate).round());
  }

  /// Validates camera permissions
  static Future<CameraPermissionStatus> checkCameraPermissions() async {
    // In a real implementation, this would check actual permissions
    // For now, simulate permission check
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // Mock permission status - in real app, use permission_handler package
    return CameraPermissionStatus.granted;
  }

  /// Requests camera permissions
  static Future<CameraPermissionStatus> requestCameraPermissions() async {
    // In a real implementation, this would request actual permissions
    // For now, simulate permission request
    await Future<void>.delayed(const Duration(milliseconds: 500));

    return CameraPermissionStatus.granted;
  }

  /// Gets available camera lenses on the device
  static Future<List<CameraLens>> getAvailableCameraLenses() async {
    // In a real implementation, this would query available cameras
    // For now, assume both front and back cameras are available
    return [CameraLens.front, CameraLens.back];
  }

  /// Checks if the specified camera lens is available
  static Future<bool> isCameraLensAvailable(CameraLens lens) async {
    final availableLenses = await getAvailableCameraLenses();
    return availableLenses.contains(lens);
  }

  /// Gets the default camera lens for face verification
  static CameraLens getDefaultCameraLens() {
    return CameraLens.front; // Front camera is preferred for face verification
  }

  /// Calculates optimal preview size based on screen dimensions
  static CameraPreviewSize calculateOptimalPreviewSize(
    double screenWidth,
    double screenHeight,
  ) {
    // Calculate aspect ratio
    final screenAspectRatio = screenWidth / screenHeight;

    // Common camera aspect ratios
    const aspectRatios = [
      (16, 9), // 16:9
      (4, 3), // 4:3
      (3, 2), // 3:2
    ];

    // Find closest aspect ratio
    var closestRatio = aspectRatios.first;
    var minDifference = double.infinity;

    for (final ratio in aspectRatios) {
      final ratioValue = ratio.$1 / ratio.$2;
      final difference = (screenAspectRatio - ratioValue).abs();

      if (difference < minDifference) {
        minDifference = difference;
        closestRatio = ratio;
      }
    }

    // Calculate preview size maintaining aspect ratio
    final targetAspectRatio = closestRatio.$1 / closestRatio.$2;

    double previewWidth;
    double previewHeight;

    if (screenAspectRatio > targetAspectRatio) {
      // Screen is wider than target ratio
      previewHeight = screenHeight;
      previewWidth = previewHeight * targetAspectRatio;
    } else {
      // Screen is taller than target ratio
      previewWidth = screenWidth;
      previewHeight = previewWidth / targetAspectRatio;
    }

    return CameraPreviewSize(
      width: previewWidth.round(),
      height: previewHeight.round(),
      aspectRatio: targetAspectRatio,
    );
  }

  /// Validates video file format
  static bool isValidVideoFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    const supportedFormats = ['mp4', 'mov', 'avi'];
    return supportedFormats.contains(extension);
  }

  /// Gets the recommended video file extension for the platform
  static String getRecommendedVideoExtension() {
    if (Platform.isIOS) {
      return 'mov';
    } else {
      return 'mp4';
    }
  }

  /// Generates a unique video filename
  static String generateVideoFilename({String? prefix}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = getRecommendedVideoExtension();
    final filePrefix = prefix ?? 'face_verification';

    return '${filePrefix}_$timestamp.$extension';
  }

  /// Estimates video file size based on configuration and duration
  static int estimateVideoFileSize(
    VideoCaptureConfig config,
    Duration duration,
  ) {
    final videoConfig = getVideoConfiguration(config.videoQuality);
    final bitrate = videoConfig['bitrate'] as int;
    final durationSeconds = duration.inSeconds;

    // File size = (bitrate * duration) / 8 (convert bits to bytes)
    // Add 10% overhead for container format
    final estimatedSize = ((bitrate * durationSeconds) / 8 * 1.1).round();

    return estimatedSize;
  }

  /// Checks if there's enough storage space for recording
  static Future<bool> hasEnoughStorageSpace(
    VideoCaptureConfig config,
    Duration duration,
  ) async {
    try {
      // In a real implementation, this would check actual available storage
      // For now, simulate storage check
      final estimatedSize = estimateVideoFileSize(config, duration);
      const minimumFreeSpace = 100 * 1024 * 1024; // 100 MB minimum

      // Mock available space (1 GB)
      const availableSpace = 1024 * 1024 * 1024;

      return availableSpace >= (estimatedSize + minimumFreeSpace);
    } catch (error) {
      // If we can't check storage, assume there's enough space
      return true;
    }
  }

  /// Gets camera configuration for face verification
  static Map<String, dynamic> getFaceVerificationCameraConfig() {
    return {
      'preferredLens': CameraLens.front,
      'enableAudio': true,
      'enableFlash': false,
      'enableZoom': false,
      'stabilization': true,
      'focusMode': 'auto',
      'exposureMode': 'auto',
      'whiteBalanceMode': 'auto',
    };
  }

  /// Validates camera configuration
  static bool isValidCameraConfig(Map<String, dynamic> config) {
    const requiredKeys = ['preferredLens', 'enableAudio'];

    for (final key in requiredKeys) {
      if (!config.containsKey(key)) {
        return false;
      }
    }

    return true;
  }
}

/// {@template camera_permission_status}
/// Status of camera permissions.
/// {@endtemplate}
enum CameraPermissionStatus {
  /// Permission granted
  granted,

  /// Permission denied
  denied,

  /// Permission permanently denied
  permanentlyDenied,

  /// Permission not determined yet
  notDetermined,
}

/// {@template camera_preview_size}
/// Represents camera preview dimensions and aspect ratio.
/// {@endtemplate}
@immutable
class CameraPreviewSize {
  /// {@macro camera_preview_size}
  const CameraPreviewSize({
    required this.width,
    required this.height,
    required this.aspectRatio,
  });

  /// Preview width in pixels
  final int width;

  /// Preview height in pixels
  final int height;

  /// Aspect ratio (width / height)
  final double aspectRatio;

  @override
  String toString() {
    return 'CameraPreviewSize(${width}x$height, '
        'ratio: ${aspectRatio.toStringAsFixed(2)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CameraPreviewSize &&
        other.width == width &&
        other.height == height &&
        (other.aspectRatio - aspectRatio).abs() < 0.01;
  }

  @override
  int get hashCode => Object.hash(width, height, aspectRatio);
}
