// Global Teardown for Playwright Tests
// This file contains cleanup logic that runs after all tests

const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 Starting global teardown...');
  
  // Clean up test data
  await cleanupTestData();
  
  // Generate test summary
  await generateTestSummary();
  
  // Clean up temporary files
  await cleanupTempFiles();
  
  console.log('✅ Global teardown completed');
}

async function cleanupTestData() {
  console.log('🗑️  Cleaning up test data...');
  
  try {
    const testDataDir = path.join(__dirname, 'test-data');
    
    if (fs.existsSync(testDataDir)) {
      // Remove temporary test files but keep configuration
      const tempFiles = fs.readdirSync(testDataDir).filter(file => 
        file.startsWith('temp-') || file.endsWith('.tmp')
      );
      
      tempFiles.forEach(file => {
        const filePath = path.join(testDataDir, file);
        fs.unlinkSync(filePath);
        console.log(`  Removed: ${file}`);
      });
    }
    
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn(`⚠️  Test data cleanup failed: ${error.message}`);
  }
}

async function generateTestSummary() {
  console.log('📊 Generating test summary...');
  
  try {
    const resultsPath = path.join(__dirname, '..', 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        environment: process.env.TEST_ENVIRONMENT || 'development',
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        appleSignInTests: {
          webTests: 0,
          configurationTests: 0,
          errorHandlingTests: 0
        }
      };
      
      // Count Apple Sign-In specific tests
      if (results.suites) {
        results.suites.forEach(suite => {
          if (suite.title?.includes('Apple Sign-In')) {
            if (suite.title.includes('Web')) {
              summary.appleSignInTests.webTests += suite.tests?.length || 0;
            } else if (suite.title.includes('Configuration')) {
              summary.appleSignInTests.configurationTests += suite.tests?.length || 0;
            } else if (suite.title.includes('Error')) {
              summary.appleSignInTests.errorHandlingTests += suite.tests?.length || 0;
            }
          }
        });
      }
      
      // Write summary
      const summaryPath = path.join(__dirname, '..', 'test-results', 'summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      // Log summary to console
      console.log('📈 Test Summary:');
      console.log(`  Total Tests: ${summary.totalTests}`);
      console.log(`  Passed: ${summary.passed}`);
      console.log(`  Failed: ${summary.failed}`);
      console.log(`  Skipped: ${summary.skipped}`);
      console.log(`  Duration: ${Math.round(summary.duration / 1000)}s`);
      console.log(`  Apple Sign-In Web Tests: ${summary.appleSignInTests.webTests}`);
      console.log(`  Apple Sign-In Config Tests: ${summary.appleSignInTests.configurationTests}`);
      console.log(`  Apple Sign-In Error Tests: ${summary.appleSignInTests.errorHandlingTests}`);
      
    } else {
      console.warn('⚠️  Test results file not found, skipping summary generation');
    }
    
  } catch (error) {
    console.warn(`⚠️  Test summary generation failed: ${error.message}`);
  }
}

async function cleanupTempFiles() {
  console.log('🗂️  Cleaning up temporary files...');
  
  try {
    const tempDirs = [
      path.join(__dirname, '..', 'temp'),
      path.join(__dirname, '..', '.tmp'),
      path.join(__dirname, '..', 'playwright-report', 'temp')
    ];
    
    tempDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`  Removed directory: ${path.basename(dir)}`);
      }
    });
    
    console.log('✅ Temporary files cleanup completed');
  } catch (error) {
    console.warn(`⚠️  Temporary files cleanup failed: ${error.message}`);
  }
}

module.exports = globalTeardown;
