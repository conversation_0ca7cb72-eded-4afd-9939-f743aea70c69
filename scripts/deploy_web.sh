#!/bin/bash

# Web Deployment Script for Bloomg Flutter
# This script builds the Flutter web app and deploys it to Firebase Hosting

set -e  # Exit on any error

echo "🚀 Starting web deployment process..."

# Step 1: Clean previous build
echo "🧹 Cleaning previous build..."
flutter clean

# Step 2: Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Step 3: Build web app
echo "🔨 Building Flutter web app..."
flutter build web

# Step 4: Ensure .well-known directory exists
echo "📁 Setting up .well-known directory..."
mkdir -p build/web/.well-known

# Step 5: Copy Apple domain association file
echo "🍎 Copying Apple domain association file..."
if [ -f "public/.well-known/apple-developer-domain-association.txt" ]; then
    cp public/.well-known/apple-developer-domain-association.txt build/web/.well-known/
    echo "✅ Apple domain association file copied"
else
    echo "⚠️  Warning: Apple domain association file not found in public/.well-known/"
fi

# Step 6: Deploy to Firebase
echo "🔥 Deploying to Firebase Hosting..."
firebase deploy --only hosting

# Step 7: Verify deployment
echo "🔍 Verifying deployment..."
echo "Web app: https://bloomg-flutter.web.app"
echo "Apple domain association: https://bloomg-flutter.web.app/.well-known/apple-developer-domain-association.txt"

# Test Apple domain association
echo "🧪 Testing Apple domain association..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" https://bloomg-flutter.web.app/.well-known/apple-developer-domain-association.txt)
if [ "$RESPONSE" = "200" ]; then
    echo "✅ Apple domain association file is accessible"
    CONTENT=$(curl -s https://bloomg-flutter.web.app/.well-known/apple-developer-domain-association.txt)
    echo "📄 Content: $CONTENT"
else
    echo "❌ Apple domain association file is not accessible (HTTP $RESPONSE)"
fi

echo "🎉 Deployment complete!"
