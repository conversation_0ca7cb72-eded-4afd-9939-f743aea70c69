#!/bin/bash

# Post-build script to fix Flutter web build issues
# This script addresses known Flutter template replacement bugs

echo "🔧 Fixing Flutter web build issues..."

# Check if build/web/index.html exists
if [ ! -f "build/web/index.html" ]; then
    echo "❌ Error: build/web/index.html not found. Run 'flutter build web' first."
    exit 1
fi

# Fix JavaScript syntax error with double quotes in service worker version
echo "🔍 Checking for JavaScript syntax errors..."

# Check if the double quote issue exists
if grep -q '""[0-9]*""' build/web/index.html; then
    echo "🐛 Found double quote syntax error in service worker version"
    
    # Extract the service worker version number
    SW_VERSION=$(grep -o '""[0-9]*""' build/web/index.html | sed 's/""//g')
    echo "📝 Service worker version: $SW_VERSION"
    
    # Fix the syntax error
    sed -i '' "s/\"\"$SW_VERSION\"\"/\"$SW_VERSION\"/g" build/web/index.html
    echo "✅ Fixed service worker version syntax"
else
    echo "✅ No service worker syntax errors found"
fi

# Check for deprecated FlutterLoader.loadEntrypoint and update to new API
if grep -q 'loadEntrypoint' build/web/index.html; then
    echo "🔄 Updating deprecated FlutterLoader.loadEntrypoint to new API..."
    
    # Create a temporary file with the new loader code
    cat > /tmp/new_loader.js << 'EOF'
      _flutter.loader.load({
        serviceWorker: {
          serviceWorkerVersion: "SW_VERSION_PLACEHOLDER",
        }
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        appRunner.runApp();
      });
EOF
    
    # Extract current service worker version
    CURRENT_SW_VERSION=$(grep -o 'serviceWorkerVersion: "[^"]*"' build/web/index.html | sed 's/serviceWorkerVersion: "\([^"]*\)"/\1/')
    
    if [ -n "$CURRENT_SW_VERSION" ]; then
        # Replace placeholder with actual version
        sed -i '' "s/SW_VERSION_PLACEHOLDER/$CURRENT_SW_VERSION/g" /tmp/new_loader.js
        
        # Replace the entire loadEntrypoint block with the new loader
        # This is a complex sed operation, so we'll use a different approach
        python3 -c "
import re
import sys

with open('build/web/index.html', 'r') as f:
    content = f.read()

# Pattern to match the entire loadEntrypoint block
pattern = r'_flutter\.loader\.loadEntrypoint\({[^}]*serviceWorker:\s*{[^}]*}[^}]*}[^}]*}\);'

# Read the new loader code
with open('/tmp/new_loader.js', 'r') as f:
    new_loader = f.read().strip()

# Replace the pattern
new_content = re.sub(pattern, new_loader, content, flags=re.DOTALL)

with open('build/web/index.html', 'w') as f:
    f.write(new_content)
"
        echo "✅ Updated to new Flutter loader API"
    else
        echo "⚠️  Could not extract service worker version for API update"
    fi
    
    # Clean up
    rm -f /tmp/new_loader.js
else
    echo "✅ Already using new Flutter loader API"
fi

echo "🎉 Web build fixes completed successfully!"
echo "📱 Your app should now load without JavaScript errors"
echo "🚀 Deploy with: firebase deploy --only hosting"
