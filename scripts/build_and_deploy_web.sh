#!/bin/bash

# Complete Flutter web build and deploy script
# This script builds the Flutter web app, fixes known issues, and deploys to Firebase

set -e  # Exit on any error

echo "🚀 Starting Flutter web build and deploy process..."

# Step 1: Clean previous build
echo "🧹 Cleaning previous build..."
flutter clean

# Step 2: Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Step 3: Build for web
echo "🔨 Building Flutter web app..."
flutter build web

# Step 4: Fix known Flutter web issues
echo "🔧 Applying post-build fixes..."
if [ -f "scripts/fix_web_build.sh" ]; then
    ./scripts/fix_web_build.sh
else
    echo "⚠️  Warning: fix_web_build.sh script not found, skipping fixes"
fi

# Step 5: Copy build to Firebase public directory
echo "📁 Copying build to Firebase public directory..."
if [ -d "public" ]; then
    rm -rf public/*
else
    mkdir -p public
fi
cp -r build/web/* public/

# Step 6: Deploy to Firebase Hosting
echo "🌐 Deploying to Firebase Hosting..."
firebase deploy --only hosting

echo ""
echo "🎉 Deployment completed successfully!"
echo "🔗 Your app is live at: https://bloomg-flutter.web.app"
echo ""
echo "📋 Summary of fixes applied:"
echo "  ✅ Fixed JavaScript syntax error in service worker version"
echo "  ✅ Updated to new Flutter loader API (removed deprecation warning)"
echo "  ✅ Ensured proper meta tag configuration"
echo ""
echo "🧪 Test your app by opening: https://bloomg-flutter.web.app"
echo "🔍 Check browser console for any remaining errors"
