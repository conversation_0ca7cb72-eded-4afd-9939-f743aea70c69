#!/bin/bash

# Apple Sign-In Test Runner Script
# This script runs comprehensive Apple Sign-In tests across all platforms

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_ENVIRONMENT="${TEST_ENVIRONMENT:-development}"
FLUTTER_WEB_PORT="${FLUTTER_WEB_PORT:-3000}"
FLUTTER_WEB_URL="http://localhost:${FLUTTER_WEB_PORT}"

echo -e "${BLUE}🚀 Apple Sign-In Test Runner${NC}"
echo -e "${BLUE}================================${NC}"
echo "Project Root: $PROJECT_ROOT"
echo "Test Environment: $TEST_ENVIRONMENT"
echo "Flutter Web URL: $FLUTTER_WEB_URL"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Cleanup function
cleanup() {
    print_section "Cleaning up"
    
    # Kill Flutter web server if running
    if [ ! -z "$FLUTTER_PID" ]; then
        echo "Stopping Flutter web server (PID: $FLUTTER_PID)"
        kill $FLUTTER_PID 2>/dev/null || true
        wait $FLUTTER_PID 2>/dev/null || true
    fi
    
    print_success "Cleanup completed"
}

# Set up cleanup trap
trap cleanup EXIT

# Change to project root
cd "$PROJECT_ROOT"

print_section "Pre-flight Checks"

# Check required tools
if ! command_exists flutter; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

if ! command_exists bundle; then
    print_error "Bundler is not installed or not in PATH"
    print_warning "Install with: gem install bundler"
    exit 1
fi

print_success "All required tools are available"

# Check Flutter analyze
print_section "Flutter Code Analysis"
echo "Running flutter analyze..."
if flutter analyze; then
    print_success "Flutter analyze passed"
else
    print_error "Flutter analyze failed"
    print_warning "Please fix analysis issues before running tests"
    exit 1
fi

# Install dependencies
print_section "Installing Dependencies"

echo "Installing Flutter dependencies..."
flutter pub get

echo "Installing Node.js dependencies..."
npm install

echo "Installing Ruby dependencies..."
cd fastlane
bundle install
cd ..

print_success "All dependencies installed"

# Setup Apple Sign-In configuration
print_section "Apple Sign-In Configuration Setup"

echo "Setting up Apple Sign-In for $TEST_ENVIRONMENT environment..."
cd fastlane

if bundle exec fastlane ios setup_apple_signin environment:$TEST_ENVIRONMENT; then
    print_success "iOS Apple Sign-In setup completed"
else
    print_warning "iOS Apple Sign-In setup failed (may be expected in CI)"
fi

if bundle exec fastlane setup_android_apple_signin; then
    print_success "Android Apple Sign-In setup completed"
else
    print_warning "Android Apple Sign-In setup failed (may be expected in CI)"
fi

if bundle exec fastlane setup_web_apple_signin; then
    print_success "Web Apple Sign-In setup completed"
else
    print_warning "Web Apple Sign-In setup failed (may be expected in CI)"
fi

cd ..

# Start Flutter web server
print_section "Starting Flutter Web Server"

echo "Starting Flutter web server on port $FLUTTER_WEB_PORT..."
flutter run -d web-server --web-port=$FLUTTER_WEB_PORT &
FLUTTER_PID=$!

echo "Flutter web server started (PID: $FLUTTER_PID)"
echo "Waiting for server to be ready..."

# Wait for server to be ready
for i in {1..30}; do
    if curl -s "$FLUTTER_WEB_URL" >/dev/null 2>&1; then
        print_success "Flutter web server is ready"
        break
    fi
    
    if [ $i -eq 30 ]; then
        print_error "Flutter web server failed to start within 30 seconds"
        exit 1
    fi
    
    echo "Waiting... ($i/30)"
    sleep 1
done

# Run Flutter unit tests
print_section "Flutter Unit Tests"

echo "Running Flutter unit tests..."
if flutter test; then
    print_success "Flutter unit tests passed"
else
    print_error "Flutter unit tests failed"
    exit 1
fi

# Run Flutter integration tests
print_section "Flutter Integration Tests"

echo "Running Flutter integration tests..."
if flutter test integration_test/apple_signin_test.dart; then
    print_success "Flutter integration tests passed"
else
    print_warning "Flutter integration tests failed (may be expected without device)"
fi

# Run Playwright web tests
print_section "Playwright Web Tests"

echo "Setting environment variables for Playwright..."
export FLUTTER_WEB_URL="$FLUTTER_WEB_URL"
export TEST_ENVIRONMENT="$TEST_ENVIRONMENT"

echo "Running Playwright tests..."
if npx playwright test tests/apple_signin_web_test.spec.js; then
    print_success "Playwright web tests passed"
else
    print_error "Playwright web tests failed"
    exit 1
fi

# Validate complete setup
print_section "Final Validation"

echo "Running complete Apple Sign-In validation..."
cd fastlane

if bundle exec fastlane validate_complete_apple_signin_setup; then
    print_success "Complete Apple Sign-In validation passed"
else
    print_warning "Complete validation failed (may be expected in CI)"
fi

cd ..

# Generate test report
print_section "Test Report Generation"

echo "Generating test reports..."

# Create reports directory
mkdir -p reports

# Copy Playwright reports
if [ -d "playwright-report" ]; then
    cp -r playwright-report reports/
    print_success "Playwright report copied to reports/"
fi

# Copy test results
if [ -d "test-results" ]; then
    cp -r test-results reports/
    print_success "Test results copied to reports/"
fi

# Generate summary
cat > reports/test-summary.md << EOF
# Apple Sign-In Test Summary

**Test Environment:** $TEST_ENVIRONMENT  
**Test Date:** $(date)  
**Flutter Web URL:** $FLUTTER_WEB_URL  

## Test Results

- ✅ Flutter Code Analysis: PASSED
- ✅ Flutter Unit Tests: PASSED
- ⚠️  Flutter Integration Tests: CONDITIONAL
- ✅ Playwright Web Tests: PASSED
- ⚠️  Apple Sign-In Configuration: CONDITIONAL

## Reports

- [Playwright HTML Report](playwright-report/index.html)
- [Test Results JSON](test-results/results.json)

## Notes

- Integration tests may fail without proper device/simulator setup
- Apple Sign-In configuration may fail in CI without proper credentials
- Web tests validate UI components and basic authentication flow

EOF

print_success "Test summary generated"

print_section "Test Completion"
print_success "All Apple Sign-In tests completed successfully!"
echo ""
echo "📊 View test reports in the 'reports/' directory"
echo "🌐 Playwright report: reports/playwright-report/index.html"
echo "📋 Test summary: reports/test-summary.md"
echo ""
print_success "Apple Sign-In is ready for deployment!"
