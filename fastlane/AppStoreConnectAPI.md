# App Store Connect API Configuration Guide

This document provides instructions for setting up App Store Connect API integration for automated Apple Sign-In configuration.

## Prerequisites

1. **Apple Developer Account**: You need an active Apple Developer Program membership
2. **Team Admin Role**: You must have Admin role in your Apple Developer team
3. **App Store Connect Access**: Access to App Store Connect with appropriate permissions

## Step 1: Create App Store Connect API Key

1. **Login to App Store Connect**
   - Go to [App Store Connect](https://appstoreconnect.apple.com)
   - Sign in with your Apple ID

2. **Navigate to API Keys**
   - Click on "Users and Access"
   - Select "Keys" tab
   - Click "Generate API Key" or the "+" button

3. **Configure API Key**
   - **Name**: `Bloomg Flutter Fastlane Automation`
   - **Access**: Select "Developer" role
   - **Download**: Download the `.p8` file immediately (you can't download it again)

4. **Note Important Information**
   - **Key ID**: 10-character identifier (e.g., `ABC123DEFG`)
   - **Issuer ID**: UUID from the Keys page (e.g., `********-1234-1234-1234-********9012`)
   - **Key File**: The downloaded `.p8` file

## Step 2: Environment Variables Setup

Set these environment variables in your CI/CD system and local development environment:

### Required Environment Variables

```bash
# Apple Developer Portal
export APPLE_ID="<EMAIL>"
export APPLE_TEAM_ID="598AQBZ36R"

# App Store Connect API
export APP_STORE_CONNECT_API_KEY_ID="ABC123DEFG"
export APP_STORE_CONNECT_API_ISSUER_ID="********-1234-1234-1234-********9012"
export APP_STORE_CONNECT_API_KEY_CONTENT="$(base64 -i /path/to/AuthKey_ABC123DEFG.p8)"

# iTunes Connect (if different from Developer Portal)
export ITUNES_CONNECT_TEAM_ID="598AQBZ36R"

# Optional: For enhanced security
export FASTLANE_PASSWORD="your-apple-id-password"
export FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD="app-specific-password"
```

### Local Development Setup

1. **Create `.env` file** (add to `.gitignore`):
```bash
# .env file for local development
APPLE_ID=<EMAIL>
APPLE_TEAM_ID=598AQBZ36R
APP_STORE_CONNECT_API_KEY_ID=ABC123DEFG
APP_STORE_CONNECT_API_ISSUER_ID=********-1234-1234-1234-********9012
APP_STORE_CONNECT_API_KEY_CONTENT=LS0tLS1CRUdJTi...base64-encoded-content...
```

2. **Load environment variables**:
```bash
# Load variables before running Fastlane
source .env
```

## Step 3: Bitbucket Pipelines Configuration

Add these environment variables to your Bitbucket repository:

1. **Go to Repository Settings**
   - Navigate to your Bitbucket repository
   - Go to "Repository settings" → "Pipelines" → "Repository variables"

2. **Add Variables** (mark sensitive ones as "Secured"):
   - `APPLE_ID` (secured)
   - `APPLE_TEAM_ID`
   - `APP_STORE_CONNECT_API_KEY_ID` (secured)
   - `APP_STORE_CONNECT_API_ISSUER_ID` (secured)
   - `APP_STORE_CONNECT_API_KEY_CONTENT` (secured)

## Step 4: Security Best Practices

### Key File Security
- **Never commit** the `.p8` file to version control
- **Store securely** in your CI/CD system's secret management
- **Rotate regularly** (recommended every 6-12 months)
- **Use base64 encoding** for environment variables

### Access Control
- **Limit API key permissions** to minimum required
- **Monitor API key usage** in App Store Connect
- **Revoke unused keys** immediately

### Base64 Encoding Commands

```bash
# macOS/Linux
base64 -i AuthKey_ABC123DEFG.p8

# Alternative (without line breaks)
base64 -i AuthKey_ABC123DEFG.p8 | tr -d '\n'

# Windows (PowerShell)
[Convert]::ToBase64String([IO.File]::ReadAllBytes("AuthKey_ABC123DEFG.p8"))
```

## Step 5: Verification

Test your configuration:

```bash
# Navigate to project root
cd /path/to/bloomg_flutter

# Install Fastlane dependencies
bundle install

# Test API connection
bundle exec fastlane ios show_apple_signin_status

# Test full setup for development environment
bundle exec fastlane ios setup_apple_signin environment:development
```

## Troubleshooting

### Common Issues

1. **Invalid API Key**
   - Verify Key ID and Issuer ID are correct
   - Ensure base64 encoding is correct (no line breaks)
   - Check that the API key hasn't been revoked

2. **Permission Errors**
   - Verify your Apple ID has Admin role
   - Check that the API key has Developer access
   - Ensure team ID is correct

3. **Network Issues**
   - Check internet connectivity
   - Verify firewall settings allow Apple API access
   - Try running from different network

### Debug Commands

```bash
# Enable verbose logging
export FASTLANE_VERBOSE=1

# Test API connection
bundle exec fastlane spaceship

# Validate environment variables
echo $APP_STORE_CONNECT_API_KEY_ID
echo $APP_STORE_CONNECT_API_ISSUER_ID
```

## API Key Permissions

The API key needs these minimum permissions:

- **Developer Portal**: Read and write access to certificates, identifiers, and profiles
- **App Store Connect**: Read access to app information
- **TestFlight**: Read access for testing (optional)

## Rate Limits

Apple API has rate limits:
- **1000 requests per hour** per API key
- **Burst limit**: 100 requests per minute
- **Concurrent limit**: 10 simultaneous requests

Fastlane automatically handles rate limiting and retries.

## Support

For issues with this configuration:

1. **Check Fastlane logs** for detailed error messages
2. **Verify Apple Developer Portal** settings manually
3. **Test API key** in App Store Connect
4. **Contact Apple Developer Support** for account-specific issues

## References

- [App Store Connect API Documentation](https://developer.apple.com/documentation/appstoreconnectapi)
- [Fastlane App Store Connect API Guide](https://docs.fastlane.tools/app-store-connect-api/)
- [Apple Developer Portal](https://developer.apple.com/account/)
- [Spaceship Documentation](https://docs.fastlane.tools/spaceship/)
