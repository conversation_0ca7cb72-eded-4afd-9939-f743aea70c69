// Apple Sign-In Integration Test for Flutter
// This test validates Apple Sign-In functionality in the Flutter app

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:bloomg_flutter/main_development.dart' as app;
import 'package:bloomg_flutter/shared/widgets/apple_sign_in_button.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Apple Sign-In Integration Tests', () {
    
    testWidgets('should display Apple Sign-In button on login page', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      expect(loginButton, findsOneWidget);
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Verify Apple Sign-In button is present
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);

      // Verify button text
      expect(find.text('Continue with Apple'), findsOneWidget);
    });

    testWidgets('should display Apple Sign-In button on signup page', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to signup page
      final signupButton = find.byKey(const Key('signup-button'));
      expect(signupButton, findsOneWidget);
      await tester.tap(signupButton);
      await tester.pumpAndSettle();

      // Verify Apple Sign-In button is present
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);

      // Verify button text
      expect(find.text('Continue with Apple'), findsOneWidget);
    });

    testWidgets('should handle Apple Sign-In button tap', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Find and tap Apple Sign-In button
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);
      
      await tester.tap(appleSignInButton);
      await tester.pump();

      // Verify loading state is shown
      // Note: In a real test, this would trigger the actual Apple Sign-In flow
      // For integration testing, we're mainly verifying the UI responds correctly
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('should maintain button state during loading', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Find Apple Sign-In button
      final appleSignInButton = find.byType(AppleSignInButton);
      
      // Verify button is initially enabled
      final AppleSignInButton buttonWidget = tester.widget(appleSignInButton);
      expect(buttonWidget.onPressed, isNotNull);
      expect(buttonWidget.isLoading, isFalse);

      // Tap the button
      await tester.tap(appleSignInButton);
      await tester.pump();

      // Verify loading state
      final AppleSignInButton loadingButtonWidget = tester.widget(appleSignInButton);
      expect(loadingButtonWidget.isLoading, isTrue);
    });

    testWidgets('should navigate correctly after successful authentication', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // This test would require mocking the authentication service
      // to simulate a successful Apple Sign-In flow
      // For now, we're testing the UI components are present and functional
      
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);
    });

    testWidgets('should display error handling UI when authentication fails', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // This test would require mocking the authentication service
      // to simulate a failed Apple Sign-In flow
      // The actual error handling would be tested with unit tests
      
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);
    });

    testWidgets('should maintain consistent styling across pages', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Test login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      final loginAppleButton = find.byType(AppleSignInButton);
      expect(loginAppleButton, findsOneWidget);

      // Get button properties
      final AppleSignInButton loginButtonWidget = tester.widget(loginAppleButton);
      
      // Navigate back and go to signup page
      await tester.pageBack();
      await tester.pumpAndSettle();

      final signupButton = find.byKey(const Key('signup-button'));
      await tester.tap(signupButton);
      await tester.pumpAndSettle();

      final signupAppleButton = find.byType(AppleSignInButton);
      expect(signupAppleButton, findsOneWidget);

      final AppleSignInButton signupButtonWidget = tester.widget(signupAppleButton);

      // Verify consistent styling (both should have same properties)
      expect(loginButtonWidget.runtimeType, equals(signupButtonWidget.runtimeType));
    });

    testWidgets('should handle rapid button taps gracefully', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      final appleSignInButton = find.byType(AppleSignInButton);
      
      // Tap multiple times rapidly
      await tester.tap(appleSignInButton);
      await tester.pump(const Duration(milliseconds: 100));
      await tester.tap(appleSignInButton);
      await tester.pump(const Duration(milliseconds: 100));
      await tester.tap(appleSignInButton);
      await tester.pump();

      // Button should handle rapid taps gracefully
      // (implementation should prevent multiple simultaneous auth attempts)
      final AppleSignInButton buttonWidget = tester.widget(appleSignInButton);
      expect(buttonWidget.isLoading, isTrue);
    });

    testWidgets('should be accessible with proper semantics', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Find Apple Sign-In button
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);

      // Verify accessibility
      final Semantics semantics = tester.widget(find.ancestor(
        of: appleSignInButton,
        matching: find.byType(Semantics),
      ).first);

      // Button should be properly labeled for screen readers
      expect(semantics.properties.label, contains('Apple'));
      expect(semantics.properties.button, isTrue);
    });

    testWidgets('should work with different screen sizes', (WidgetTester tester) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(320, 568)); // iPhone SE
      
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login page
      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Verify button is visible and properly sized
      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);

      // Test with tablet size
      await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad
      await tester.pumpAndSettle();

      // Button should still be visible and properly sized
      expect(appleSignInButton, findsOneWidget);

      // Reset to default size
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone 8
    });

  });

  group('Apple Sign-In Error Handling', () {
    
    testWidgets('should handle network connectivity issues', (WidgetTester tester) async {
      // This would require mocking network conditions
      // For now, we ensure the UI components are present
      
      app.main();
      await tester.pumpAndSettle();

      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);
    });

    testWidgets('should handle authentication service errors', (WidgetTester tester) async {
      // This would require mocking authentication service errors
      // For now, we ensure the UI components are present
      
      app.main();
      await tester.pumpAndSettle();

      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);
    });

  });

  group('Apple Sign-In Cross-Platform Compatibility', () {
    
    testWidgets('should work on iOS simulator', (WidgetTester tester) async {
      // This test runs on iOS simulator to verify platform-specific functionality
      
      app.main();
      await tester.pumpAndSettle();

      final loginButton = find.byKey(const Key('login-button'));
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      final appleSignInButton = find.byType(AppleSignInButton);
      expect(appleSignInButton, findsOneWidget);

      // On iOS, the button should be functional
      await tester.tap(appleSignInButton);
      await tester.pump();

      // Verify loading state
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

  });

}
